{"version": 3, "names": ["_classPrivateMethodSet", "TypeError"], "sources": ["../../src/helpers/classPrivateMethodSet.js"], "sourcesContent": ["/* @minVersion 7.1.6 */\n/* @onlyBabel7 */\n\n// use readOnlyError instead of attemptSet\n\nexport default function _classPrivateMethodSet() {\n  throw new TypeError(\"attempted to reassign private method\");\n}\n"], "mappings": ";;;;;;AAKe,SAASA,sBAAsBA,CAAA,EAAG;EAC/C,MAAM,IAAIC,SAAS,CAAC,sCAAsC,CAAC;AAC7D", "ignoreList": []}