# Core Framework
fastapi>=0.104.1
uvicorn[standard]==0.24.0
# pydantic==2.5.0
pydantic>=2.7.0,<3.0.0
pydantic-settings>=2.5.2

# Database & ORM
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.1

# Redis & Caching
redis==5.0.1
hiredis==2.2.3

# Vector Database
qdrant-client==1.7.0
sentence-transformers==2.2.2

# File Processing
pandas==2.1.4
openpyxl==3.1.2
xlrd==2.0.1
PyPDF2==3.0.1
python-docx==1.1.0
python-multipart>==0.0.9
aiofiles==23.2.0

# AI & ML
# openai>=1.6.1,<2.0.0
openai==1.35.2
langchain==0.0.350
langchain-openai==0.0.2
tiktoken==0.5.2

# MCP (Model Context Protocol)
mcp>=1.9.3
websockets==12.0

# Authentication & Security
# python-jose[cryptography]==3.3.0
python-jose-cryptodome==1.3.2
passlib[bcrypt]==1.7.4

# HTTP & API
httpx>=0.27
requests==2.31.0

# Utilities
python-magic==0.4.27
celery==5.3.4
flower==2.0.1
python-dotenv==1.0.0
structlog==23.2.0

# Monitoring & Logging
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
mypy==1.7.1
